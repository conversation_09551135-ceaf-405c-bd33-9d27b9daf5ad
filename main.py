import httpx
import sys
import json

OLLAMA_URL = "http://localhost:11434/api/generate"  # adjust if different

def stream_generate(prompt: str, model: str = "llama3.1"):
    payload = {
        "model": model,
        "prompt": prompt,
        "stream": True  # ask for streaming; Ollama uses this flag for incremental tokens
    }

    headers = {
        "Content-Type": "application/json"
        # add Authorization header here if your setup requires it, e.g.:
        # "Authorization": "Bearer YOUR_TOKEN"
    }

    with httpx.stream("POST", OLLAMA_URL, json=payload, headers=headers, timeout=None) as resp:
        resp.raise_for_status()
        buffer = ""
        for chunk in resp.iter_bytes(chunk_size=1024):
            if not chunk:
                continue
            try:
                text = chunk.decode("utf-8")
            except UnicodeDecodeError:
                # skip undecodable bits
                continue

            buffer += text
            # Ollama streams newline-delimited JSON objects.
            while "\n" in buffer:
                line, buffer = buffer.split("\n", 1)
                line = line.strip()
                if not line:
                    continue
                try:
                    obj = json.loads(line)
                except json.JSONDecodeError:
                    # partial/incomplete JSON; wait for more
                    continue

                # Depending on <PERSON>llama's exact response shape, adapt this.
                # Commonly there is something like {"token": "...", "done": false}
                token = obj.get("token") or obj.get("text") or ""
                if token:
                    # Print without newline, flush so user sees progressive output
                    print(token, end="", flush=True)

                # If there's a done flag or final indicator, break if needed
                if obj.get("done") is True:
                    break
        print()  # final newline

def main():
    try:
        prompt = input("Enter your prompt: ")
    except KeyboardInterrupt:
        print("\nCanceled.")
        sys.exit(0)

    print("\n=== Streaming response ===\n")
    try:
        stream_generate(prompt)
    except httpx.HTTPStatusError as e:
        print(f"\nRequest failed: {e.response.status_code} - {e.response.text}")
    except Exception as e:
        print(f"\nError during generation: {e}")

if __name__ == "__main__":
    main()
